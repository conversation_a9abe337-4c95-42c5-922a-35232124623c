#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线编辑器使用示例
"""

import sys
from Qt import QtWidgets
from lsr.maya.anim_tools.FaceAnim_Toolkit.ui.eye_blink_tab.bezier_curve_editor import BezierCurveEditor

def set_key_and_clear(obj_name, attr_name, layerIndex=0, *args, **kwargs):
    """
    Get the animation nodes for the translation and rotation of an object.

    Args:
        obj_name (str): The object to get the transform nodes for.
        attr_name (str): attribute name to get the animation nodes for.

    Returns:
        nodes ( list of FBAnimationNode ): The transform nodes for the object.
    """
    obj = fb.FBFindModelByLabelName(obj_name)
    fb.FBSystem().CurrentTake.SetCurrentLayer(layerIndex)

    return_nodes = []
    prop = obj.PropertyList.Find(attr_name)
    if prop:
        anim_nodes = prop.GetAnimationNode()
        if anim_nodes is None:
            prop.SetAnimated(True)
            anim_nodes = prop.GetAnimationNode()
        interpolation_type = fb.FBInterpolation.kFBInterpolationLinear
        anim_nodes.KeyAdd(
            fb.FBTime(0, 0, 0, 0),
            [0, 0, 1],
            interpolation_type)
        return_nodes.append(anim_nodes.Nodes)
    fb.FBSystem().Scene.Evaluate()

    for nodes in return_nodes:
        for node in nodes:
            curve = node.FCurve
            curve.EditClear()
    prop.Data = fb.FBVector3d((0, 0, 0))
    playback = fb.FBPlayerControl()
    playback.Goto(fb.FBTime(0, 0, 0, 1))
    playback.Goto(fb.FBTime(0, 0, 0, 0))
    fb.FBSystem().Scene.Evaluate()


class BezierCurveController(QtWidgets.QWidget):

    def __init__(self, parent=None, *args, **kwargs):
        super(BezierCurveController, self).__init__(parent)
        self._init_ui()
        self._init_style()

    def _init_ui(self, *args, **kwargs):
        """初始化UI"""
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        self.setLayout(main_layout)

        self.editor = BezierCurveEditor(self)
        main_layout.addWidget(self.editor)

    def _init_style(self, *args, **kwargs):
        """初始化样式"""
        self.editor.setStyleSheet("""
                QWidget {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    font-family: Arial;
                    font-size: 12px;
                }

                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 10px;
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }

                QPushButton {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 5px;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #505050;
                }

                QPushButton:pressed {
                    background-color: #303030;
                }

                QSpinBox, QDoubleSpinBox {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 2px;
                }

                QComboBox {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 2px;
                }

                QCheckBox {
                    spacing: 5px;
                }

                QCheckBox::indicator {
                    width: 13px;
                    height: 13px;
                }

                QCheckBox::indicator:unchecked {
                    background-color: #404040;
                    border: 1px solid #606060;
                }

                QCheckBox::indicator:checked {
                    background-color: #0078d4;
                    border: 1px solid #0078d4;
                }
            """)